import { useState, useEffect } from 'react'
import { useParams, Navigate } from 'react-router-dom'
import { MapPin, Clock, DollarSign, Briefcase, Building2, Users, Calendar } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { PublicCandidateForm } from '@/components/forms/PublicCandidateForm'
import { useToast } from '@/hooks/use-toast'
import { api } from '@/lib/api'
import { formatDate } from '@/lib/utils'

interface IJob {
  _id: string
  title: string
  description: string
  qualifications?: string[]
  responsibilities?: string[]
  location: string
  jobType: 'full_time' | 'part_time' | 'contract' | 'internship'
  salaryRange?: {
    min: number
    max: number
    currency: string
    period: string
  }
  openings: number
  status: 'active' | 'draft' | 'paused' | 'closed' | 'cancelled'
  createdAt: string | Date
  postedDate?: string | Date
  organization: string | {
    _id: string
    name: string
    description?: string
  }
}

interface PublicCandidateData {
  name: string
  email: string
  phone: string
  resumeLink: string
  contactInfo: {
    linkedin?: string
    github?: string
    address?: string
  }
}

export function PublicJobApplication() {
  const { jobId } = useParams<{ jobId: string }>()
  const { toast } = useToast()
  const [job, setJob] = useState<IJob | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [submitting, setSubmitting] = useState(false)
  const [submitted, setSubmitted] = useState(false)

  useEffect(() => {
    if (!jobId) return

    const fetchJob = async () => {
      try {
        setLoading(true)
        // Use regular job endpoint
        const jobData = await api.get<IJob>(`/job/${jobId}`)
        console.log('📋 Job data received:', jobData)
        console.log('📋 Job status from API:', jobData.status)
        console.log('📋 Job status type:', typeof jobData.status)
        console.log('📋 All job keys:', Object.keys(jobData))
        setJob(jobData)
      } catch (error: any) {
        console.error('Error fetching job:', error)
        setError(error?.message || 'Failed to load job details')
      } finally {
        setLoading(false)
      }
    }

    fetchJob()
  }, [jobId])

  const handleSubmit = async (data: PublicCandidateData) => {
    if (!job) return

    try {
      setSubmitting(true)

      // Submit candidate application using regular endpoint
      await api.post('/candidate', {
        name: data.name,
        email: data.email,
        phone: data.phone,
        resumeLink: data.resumeLink,
        jobId: typeof job._id === 'string' ? job._id : String(job._id),
        stage: 'registered', // Use valid stage value
        status: 'registered', // Use valid StageStatus enum value
        source: 'public_application',
        contactInfo: {
          email: data.email, // Include email in contactInfo as required
          phone: data.phone,
          linkedin: data.contactInfo.linkedin,
          github: data.contactInfo.github,
          address: data.contactInfo.address
        }
      })

      setSubmitted(true)
      toast({
        title: "Application Submitted",
        description: `Your application for "${job.title}" has been submitted successfully!`,
      })
    } catch (error: any) {
      console.error('Error submitting application:', error)
      toast({
        title: "Submission Failed",
        description: error?.message || "Failed to submit your application. Please try again.",
        variant: "destructive",
      })
    } finally {
      setSubmitting(false)
    }
  }

  if (!jobId) {
    return <Navigate to="/" replace />
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading job details...</p>
        </div>
      </div>
    )
  }

  if (error || !job) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="max-w-md w-full mx-4">
          <CardContent className="text-center py-8">
            <div className="text-red-500 mb-4">
              <Briefcase className="h-12 w-12 mx-auto" />
            </div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Job Not Found</h2>
            <p className="text-gray-600 mb-4">
              {error || 'The job you are looking for does not exist or is no longer available.'}
            </p>
            <Button onClick={() => window.location.href = '/'}>
              Go to Homepage
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  console.log('🔍 Current job state:', job)
  console.log('🔍 Job status from state:', job.status)
  console.log('🔍 Job status type from state:', typeof job.status)
  console.log('🔍 Is active?', job.status === 'active')

  if (job.status !== 'active') {
    console.log('❌ Job is not active, showing unavailable message')
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="max-w-md w-full mx-4">
          <CardContent className="text-center py-8">
            <div className="text-yellow-500 mb-4">
              <Briefcase className="h-12 w-12 mx-auto" />
            </div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Job Not Available</h2>
            <p className="text-gray-600 mb-4">
              This job is currently {job.status} and not accepting applications.
            </p>
            <Button onClick={() => window.location.href = '/'}>
              Go to Homepage
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  console.log('✅ Job is active, showing application form')

  if (submitted) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="max-w-md w-full mx-4">
          <CardContent className="text-center py-8">
            <div className="text-green-500 mb-4">
              <Briefcase className="h-12 w-12 mx-auto" />
            </div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Application Submitted!</h2>
            <p className="text-gray-600 mb-4">
              Thank you for applying to "{job.title}".
              We'll review your application and get back to you soon.
            </p>
            <Button onClick={() => window.location.href = '/'}>
              Go to Homepage
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  const formatJobType = (jobType: string) => {
    return jobType.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ')
  }

  const formatSalary = (salaryRange?: { min: number; max: number; currency: string; period: string }) => {
    if (!salaryRange || !salaryRange.min || !salaryRange.max) return 'Not specified'
    const { min, max, currency, period } = salaryRange
    return `${currency}${min.toLocaleString()} - ${currency}${max.toLocaleString()} ${period}`
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="max-w-4xl mx-auto px-4 py-6">
          <div className="flex items-center space-x-3 mb-4">
            <Building2 className="h-8 w-8 text-blue-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">PlacedHQ</h1>
              <p className="text-sm text-gray-600">Public Job Application</p>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Job Details */}
          <div className="lg:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="text-2xl font-bold text-gray-900 mb-2">
                      {job.title}
                    </CardTitle>
                    <div className="flex items-center space-x-4 text-sm text-gray-600">
                      <div className="flex items-center space-x-1">
                        <Building2 className="h-4 w-4" />
                        <span>
                          {typeof job.organization === 'object' && job.organization?.name
                            ? job.organization.name
                            : 'Company'}
                        </span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <MapPin className="h-4 w-4" />
                        <span>{job.location}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-4 w-4" />
                        <span>Posted {formatDate(job.postedDate || job.createdAt)}</span>
                      </div>
                    </div>
                  </div>
                  <Badge variant="secondary" className="bg-green-100 text-green-800">
                    {formatJobType(job.jobType)}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="flex items-center space-x-2">
                    <DollarSign className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium">Salary</p>
                      <p className="text-sm text-gray-600">{formatSalary(job.salaryRange)}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Users className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium">Openings</p>
                      <p className="text-sm text-gray-600">{job.openings} position{job.openings !== 1 ? 's' : ''}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Clock className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium">Type</p>
                      <p className="text-sm text-gray-600">{formatJobType(job.jobType)}</p>
                    </div>
                  </div>
                </div>

                <Separator />

                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Job Description</h3>
                  <div className="prose prose-sm max-w-none text-gray-700">
                    <p className="whitespace-pre-wrap">{job.description}</p>
                  </div>
                </div>

                {job.responsibilities && job.responsibilities.length > 0 && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-3">Responsibilities</h3>
                    <ul className="list-disc list-inside space-y-1 text-gray-700">
                      {job.responsibilities.map((responsibility, index) => (
                        <li key={index}>{responsibility}</li>
                      ))}
                    </ul>
                  </div>
                )}

                {job.qualifications && job.qualifications.length > 0 && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-3">Qualifications</h3>
                    <ul className="list-disc list-inside space-y-1 text-gray-700">
                      {job.qualifications.map((qualification, index) => (
                        <li key={index}>{qualification}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Application Form */}
          <div className="lg:col-span-1">
            <Card className="sticky top-8">
              <CardHeader>
                <CardTitle className="text-lg font-semibold">Apply for this Position</CardTitle>
                <p className="text-sm text-gray-600">
                  Fill out the form below to submit your application.
                </p>
              </CardHeader>
              <CardContent>
                <PublicCandidateForm
                  onSubmit={handleSubmit}
                  isLoading={submitting}
                />
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
