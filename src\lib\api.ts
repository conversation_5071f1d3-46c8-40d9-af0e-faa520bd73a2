import { getErrorMessage } from './utils'

// API Configuration - Use empty string to leverage Vite proxy
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || ''

// API Error Class
export class ApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public data?: any
  ) {
    super(message)
    this.name = 'ApiError'
  }
}

// Request configuration interface
interface RequestConfig {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  headers?: Record<string, string>
  body?: any
  params?: Record<string, string | number | boolean>
}

// Helper function to convert ObjectIds to strings
function transformObjectIds(obj: any): any {
  if (obj === null || obj === undefined) {
    return obj
  }

  if (Array.isArray(obj)) {
    return obj.map(transformObjectIds)
  }

  if (typeof obj === 'object') {
    // Debug logging
    if (obj.constructor) {
  
    }

    // Handle ObjectId objects - multiple detection methods
    if (obj.constructor && obj.constructor.name === 'ObjectId') {
      console.log('Converting ObjectId to string:', obj.toString())
      return obj.toString()
    }

    // Handle ObjectId-like objects with toString method
    if (obj.toString && typeof obj.toString === 'function' &&
        obj.constructor && obj.constructor.name &&
        obj.constructor.name.includes('ObjectId')) {
      console.log('Converting ObjectId-like to string:', obj.toString())
      return obj.toString()
    }

    // Handle objects that look like ObjectIds (24 character hex strings)
    if (typeof obj === 'object' && obj._bsontype === 'ObjectId') {
      console.log('Converting BSON ObjectId to string:', obj.toString())
      return obj.toString()
    }

    // Handle regular objects
    const transformed: any = {}
    for (const [key, value] of Object.entries(obj)) {
      transformed[key] = transformObjectIds(value)
    }
    return transformed
  }

  return obj
}

// Base API client class
class ApiClient {
  private baseURL: string
  private defaultHeaders: Record<string, string>

  constructor(baseURL: string) {
    this.baseURL = baseURL
    this.defaultHeaders = {
      'Content-Type': 'application/json',
    }
  }

  // Cookie-based authentication - no need for manual token management
  // The browser automatically includes cookies in requests

  // Build URL with query parameters
  private buildUrl(endpoint: string, params?: Record<string, string | number | boolean>): string {
    // Handle relative base URLs (like /api) for Vite proxy
    const fullUrl = this.baseURL.startsWith('http')
      ? `${this.baseURL}${endpoint}`
      : `${window.location.origin}${this.baseURL}${endpoint}`

    const url = new URL(fullUrl)

    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          url.searchParams.append(key, String(value))
        }
      })
    }

    return url.toString()
  }

  // Make HTTP request
  private async request<T>(endpoint: string, config: RequestConfig): Promise<T> {
    const url = this.buildUrl(endpoint, config.params)

    console.log(`🌐 Making ${config.method} request to:`, url)

    const requestConfig: RequestInit = {
      method: config.method,
      headers: {
        ...this.defaultHeaders,
        ...config.headers,
      },
      credentials: 'include', // Include cookies for authentication
    }

    // Add body for non-GET requests
    if (config.body && config.method !== 'GET') {
      if (config.body instanceof FormData) {
        // Remove Content-Type header for FormData (browser will set it with boundary)
        const headers = requestConfig.headers as Record<string, string>
        delete headers['Content-Type']
        requestConfig.body = config.body
      } else {
        requestConfig.body = JSON.stringify(config.body)
      }
    }

    try {
      const response = await fetch(url, requestConfig)
      
      // Handle response parsing - always read as text first to avoid body consumption issues
      const contentType = response.headers.get('content-type')
      console.log('🌐 Response Content-Type:', contentType)
      console.log('🌐 Response Status:', response.status)
      console.log('🌐 Response Headers:', Object.fromEntries(response.headers.entries()))

      // Always read as text first to avoid "body already consumed" error
      const textData = await response.text()
      console.log('📄 Raw response text (first 200 chars):', textData.substring(0, 200) + '...')
      console.log('📄 Raw response text (full):', textData)

      let data: any

      if (contentType && contentType.includes('application/json')) {
        console.log('✅ Attempting to parse as JSON')
        try {
          // Try standard JSON parsing first
          data = JSON.parse(textData)
          console.log('✅ Standard JSON parsing successful')
        } catch (jsonError) {
          console.log('❌ Standard JSON parsing failed, trying ObjectId conversion')
          console.log('Original error:', jsonError)

          // Handle MongoDB ObjectId format - more comprehensive conversion
          let jsonString = textData
            // Convert all variations of ObjectId constructors to strings
            .replace(/new ObjectId\(['"]([^'"]+)['"]\)/g, '"$1"')
            .replace(/ObjectId\(['"]([^'"]+)['"]\)/g, '"$1"')
            .replace(/ObjectId\('([^']+)'\)/g, '"$1"')
            .replace(/ObjectId\("([^"]+)"\)/g, '"$1"')
            // Handle ObjectId without quotes (in case backend sends it)
            .replace(/new ObjectId\(([a-f0-9]{24})\)/g, '"$1"')
            .replace(/ObjectId\(([a-f0-9]{24})\)/g, '"$1"')
            // Handle any remaining ObjectId patterns
            .replace(/new\s+ObjectId\s*\(\s*['"]?([a-f0-9]{24})['"]?\s*\)/g, '"$1"')
            .replace(/ObjectId\s*\(\s*['"]?([a-f0-9]{24})['"]?\s*\)/g, '"$1"')

          console.log('🔄 After ObjectId conversion:', jsonString.substring(0, 500) + '...')

          try {
            // Try parsing the cleaned JSON
            data = JSON.parse(jsonString)
            console.log('✅ ObjectId conversion successful')
          } catch (secondError) {
            console.log('❌ ObjectId conversion failed, trying comprehensive cleanup')
            console.log('Second error:', secondError)

            // More aggressive cleanup for malformed JSON
            jsonString = jsonString
              // Fix unquoted property names (but be careful not to break quoted strings)
              .replace(/([{,]\s*)([a-zA-Z_$][a-zA-Z0-9_$]*)\s*:/g, '$1"$2":')
              // Convert single quotes to double quotes (but not inside already quoted strings)
              .replace(/:\s*'([^']*)'/g, ': "$1"')
              // Fix trailing commas in objects and arrays
              .replace(/,(\s*[}\]])/g, '$1')
              // Handle any remaining ObjectId patterns that might have been missed
              .replace(/new\s+ObjectId\s*\([^)]*\)/g, '""')
              .replace(/ObjectId\s*\([^)]*\)/g, '""')
              // Fix any malformed ObjectId strings that got partially converted
              .replace(/"[a-f0-9]{1,23}$/g, '""')  // Incomplete ObjectId at end of string
              .replace(/"[a-f0-9]{25,}"/g, '""')   // Too long ObjectId

            try {
              data = JSON.parse(jsonString)
              console.log('✅ Comprehensive cleanup successful')
            } catch (finalError) {
              console.error('❌ All parsing attempts failed:', finalError)
              console.error('Final JSON string (first 1000 chars):', jsonString.substring(0, 1000))

              // Last resort: try to use Function constructor to safely evaluate JS object
              try {
                console.log('🔄 Trying Function constructor approach...')
                const func = new Function('return ' + textData.replace(/new ObjectId\(['"]([^'"]+)['"]\)/g, '"$1"'))
                data = func()
                console.log('✅ Function constructor approach successful')
              } catch (funcError) {
                console.error('❌ Function constructor approach failed:', funcError)
                const errorMessage = finalError instanceof Error ? finalError.message : 'Unknown parsing error'
                throw new ApiError(`Failed to parse JSON response: ${errorMessage}`, 500)
              }
            }
          }
        }

        // Transform ObjectIds to strings
        data = transformObjectIds(data)
      } else {
        console.log('❌ Not JSON content type, using as text')
        data = textData
      }

      if (!response.ok) {
        // Handle specific error cases
        let errorMessage = data?.error || data?.message || `HTTP ${response.status}: ${response.statusText}`

        console.error(`❌ API Error ${response.status}:`, {
          url,
          status: response.status,
          statusText: response.statusText,
          data,
          errorMessage
        })

        // Handle authentication errors
        if (response.status === 401) {
          // Clear authentication state
          localStorage.removeItem('isAuthenticated')
          errorMessage = 'Your session has expired. Please log in again.'
        }

        // Handle validation errors
        if (response.status === 400 && data?.details) {
          errorMessage = `${errorMessage}: ${data.details}`
        }

        throw new ApiError(errorMessage, response.status, data)
      }

      // Check for error responses that come with 200 status codes
      if (data && typeof data === 'object' && data.error) {
        console.error(`❌ API Error (200 with error):`, {
          url,
          data,
          errorMessage: data.error
        })
        throw new ApiError(data.error, 400, data) // Treat as 400 error
      }

      return data
    } catch (error) {
      if (error instanceof ApiError) {
        throw error
      }

      // Network or other errors
      console.error('🚨 Network/Connection Error:', {
        url,
        error: error,
        message: getErrorMessage(error)
      })

      throw new ApiError(
        getErrorMessage(error),
        0,
        null
      )
    }
  }

  // HTTP Methods
  async get<T>(endpoint: string, params?: Record<string, string | number | boolean>): Promise<T> {
    return this.request<T>(endpoint, { method: 'GET', params })
  }

  async post<T>(endpoint: string, data?: any, headers?: Record<string, string>): Promise<T> {
    return this.request<T>(endpoint, { method: 'POST', body: data, headers })
  }

  async put<T>(endpoint: string, data?: any, headers?: Record<string, string>): Promise<T> {
    return this.request<T>(endpoint, { method: 'PUT', body: data, headers })
  }

  async patch<T>(endpoint: string, data?: any, headers?: Record<string, string>): Promise<T> {
    return this.request<T>(endpoint, { method: 'PATCH', body: data, headers })
  }

  async delete<T>(endpoint: string, params?: Record<string, string | number | boolean>): Promise<T> {
    return this.request<T>(endpoint, { method: 'DELETE', params })
  }

  // File upload helper
  async uploadFile<T>(endpoint: string, file: File, additionalData?: Record<string, any>): Promise<T> {
    const formData = new FormData()
    formData.append('file', file)
    
    if (additionalData) {
      Object.entries(additionalData).forEach(([key, value]) => {
        formData.append(key, String(value))
      })
    }

    return this.request<T>(endpoint, { method: 'POST', body: formData })
  }
}

// Create and export API client instance
export const api = new ApiClient(API_BASE_URL)

// Export types for use in other files
export type { RequestConfig }

// Utility function to handle API errors in components
export function handleApiError(error: unknown): string {
  if (error instanceof ApiError) {
    return error.message
  }
  return getErrorMessage(error)
}

// Mock data for development (remove when backend is ready)
export const mockApi = {
  // Organizations
  organizations: [
    {
      _id: '1',
      name: 'Tech Corp',
      domain: 'techcorp.com',
      address: '123 Tech Street, Silicon Valley, CA',
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-01'),
    },
    {
      _id: '2',
      name: 'StartupXYZ',
      domain: 'startupxyz.com',
      address: '456 Innovation Ave, Austin, TX',
      createdAt: new Date('2024-01-15'),
      updatedAt: new Date('2024-01-15'),
    },
    {
      _id: '3',
      name: 'Global Solutions Inc',
      domain: 'globalsolutions.com',
      address: '789 Business Plaza, New York, NY',
      createdAt: new Date('2024-02-01'),
      updatedAt: new Date('2024-02-01'),
    },
    {
      _id: '4',
      name: 'Digital Innovations',
      domain: 'digitalinnovations.io',
      address: '',
      createdAt: new Date('2024-02-15'),
      updatedAt: new Date('2024-02-15'),
    },
    {
      _id: '5',
      name: 'Future Systems',
      domain: 'futuresystems.net',
      address: '321 Future Blvd, Seattle, WA',
      createdAt: new Date('2024-03-01'),
      updatedAt: new Date('2024-03-01'),
    },
  ],
  
  // Recruiters
  recruiters: [
    {
      _id: '1',
      name: 'John Doe',
      email: '<EMAIL>',
      organization: '1',
      password: 'hashed_password',
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-01'),
    },
    {
      _id: '2',
      name: 'Jane Smith',
      email: '<EMAIL>',
      organization: '2',
      password: 'hashed_password',
      createdAt: new Date('2024-01-15'),
      updatedAt: new Date('2024-01-15'),
    },
  ],

  // Jobs
  jobs: [
    {
      _id: '1',
      title: 'Senior Frontend Developer',
      description: 'We are looking for an experienced Frontend Developer to join our team and help build amazing user experiences.',
      department: 'Engineering',
      location: 'San Francisco, CA',
      jobType: 'full_time',
      workLocation: 'hybrid',
      experienceLevel: 'senior',
      requiredSkills: [
        { name: 'React', level: 'required', yearsRequired: 3 },
        { name: 'TypeScript', level: 'required', yearsRequired: 2 },
        { name: 'Node.js', level: 'preferred', yearsRequired: 1 }
      ],
      qualifications: [
        'Bachelor\'s degree in Computer Science or related field',
        '5+ years of frontend development experience',
        'Strong knowledge of React and modern JavaScript'
      ],
      responsibilities: [
        'Develop and maintain frontend applications',
        'Collaborate with design and backend teams',
        'Write clean, maintainable code'
      ],
      salaryRange: {
        min: 120000,
        max: 160000,
        currency: 'USD',
        period: 'yearly'
      },
      status: 'active',
      openings: 2,
      applicationDeadline: new Date('2024-12-31'),
      startDate: new Date('2024-04-01'),
      postedDate: new Date('2024-03-01'),
      // Workflow tracking fields
      workflowStatus: 'configured',
      workflowId: undefined,
      workflowStartedAt: undefined,
      workflowStoppedAt: undefined,
      createdAt: new Date('2024-03-01'),
      updatedAt: new Date('2024-03-01'),
      createdBy: '1', // John Doe
      organization: '1' // Tech Corp
    },
    {
      _id: '2',
      title: 'Product Manager',
      description: 'Join our product team to drive product strategy and work with cross-functional teams to deliver exceptional products.',
      department: 'Product',
      location: 'Austin, TX',
      jobType: 'full_time',
      workLocation: 'remote',
      experienceLevel: 'mid',
      requiredSkills: [
        { name: 'Product Management', level: 'required', yearsRequired: 3 },
        { name: 'Agile/Scrum', level: 'required', yearsRequired: 2 },
        { name: 'Data Analysis', level: 'preferred', yearsRequired: 1 }
      ],
      qualifications: [
        'MBA or equivalent experience',
        '3+ years of product management experience',
        'Experience with agile development methodologies'
      ],
      responsibilities: [
        'Define product roadmap and strategy',
        'Work with engineering and design teams',
        'Analyze user feedback and market trends'
      ],
      salaryRange: {
        min: 100000,
        max: 140000,
        currency: 'USD',
        period: 'yearly'
      },
      status: 'active',
      openings: 1,
      applicationDeadline: new Date('2024-11-30'),
      startDate: new Date('2024-05-01'),
      postedDate: new Date('2024-03-15'),
      // Workflow tracking fields
      workflowStatus: 'running',
      workflowId: 'workflow_12345',
      workflowStartedAt: new Date('2024-03-16'),
      workflowStoppedAt: undefined,
      createdAt: new Date('2024-03-15'),
      updatedAt: new Date('2024-03-15'),
      createdBy: '2', // Jane Smith
      organization: '2' // StartupXYZ
    },
    {
      _id: '3',
      title: 'Marketing Intern',
      description: 'Great opportunity for students to gain hands-on experience in digital marketing and content creation.',
      department: 'Marketing',
      location: 'New York, NY',
      jobType: 'internship',
      workLocation: 'onsite',
      experienceLevel: 'entry',
      requiredSkills: [
        { name: 'Social Media', level: 'preferred', yearsRequired: 0 },
        { name: 'Content Writing', level: 'preferred', yearsRequired: 0 },
        { name: 'Adobe Creative Suite', level: 'nice_to_have', yearsRequired: 0 }
      ],
      qualifications: [
        'Currently enrolled in Marketing, Communications, or related field',
        'Strong written and verbal communication skills',
        'Enthusiasm for digital marketing'
      ],
      responsibilities: [
        'Assist with social media content creation',
        'Support marketing campaigns',
        'Conduct market research'
      ],
      salaryRange: {
        min: 20,
        max: 25,
        currency: 'USD',
        period: 'hourly'
      },
      status: 'draft',
      openings: 1,
      applicationDeadline: new Date('2024-10-15'),
      startDate: new Date('2024-06-01'),
      postedDate: new Date('2024-03-20'),
      createdAt: new Date('2024-03-20'),
      updatedAt: new Date('2024-03-20'),
      createdBy: '1', // John Doe
      organization: '3' // Global Solutions Inc
    }
  ],

  // Candidates
  candidates: [
    {
      _id: '1',
      sourceUid: 'cand_001',
      name: 'Alice Johnson',
      email: '<EMAIL>',
      phone: '******-0123',
      resumeLink: 'https://example.com/resumes/alice-johnson.pdf',
      jobId: '1', // Senior Frontend Developer
      stage: 'veda-review',
      expectedSalary: 95000,
      contactInfo: {
        email: '<EMAIL>',
        phone: '******-0123',
        linkedin: 'https://linkedin.com/in/alice-johnson',
        github: 'https://github.com/alice-johnson',
        address: '123 Tech Street, San Francisco, CA 94105'
      },
      status: 'in_progress',
      source: 'LinkedIn',
      createdAt: new Date('2024-01-15T10:30:00Z'),
      updatedAt: new Date('2024-01-16T14:20:00Z'),
      createdBy: '1'
    },
    {
      _id: '2',
      sourceUid: 'cand_002',
      name: 'Bob Smith',
      email: '<EMAIL>',
      phone: '******-0124',
      resumeLink: 'https://example.com/resumes/bob-smith.pdf',
      jobId: '2', // Product Manager
      stage: 'screening',
      expectedSalary: 110000,
      contactInfo: {
        email: '<EMAIL>',
        phone: '******-0124',
        linkedin: 'https://linkedin.com/in/bob-smith',
        address: '456 Business Ave, Austin, TX 78701'
      },
      status: 'pending_schedule',
      source: 'Company Website',
      createdAt: new Date('2024-01-14T09:15:00Z'),
      updatedAt: new Date('2024-01-15T16:45:00Z'),
      createdBy: '2'
    },
    {
      _id: '3',
      sourceUid: 'cand_003',
      name: 'Carol Davis',
      email: '<EMAIL>',
      phone: '******-0125',
      resumeLink: 'https://example.com/resumes/carol-davis.pdf',
      jobId: '1', // Senior Frontend Developer
      stage: 'assessment',
      expectedSalary: 88000,
      contactInfo: {
        email: '<EMAIL>',
        phone: '******-0125',
        linkedin: 'https://linkedin.com/in/carol-davis',
        github: 'https://github.com/carol-davis',
        address: '789 Developer Lane, Seattle, WA 98101'
      },
      status: 'awaiting_result',
      source: 'Referral',
      createdAt: new Date('2024-01-13T11:00:00Z'),
      updatedAt: new Date('2024-01-17T10:30:00Z'),
      createdBy: '1'
    },
    {
      _id: '4',
      sourceUid: 'cand_004',
      name: 'David Wilson',
      email: '<EMAIL>',
      phone: '******-0126',
      resumeLink: 'https://example.com/resumes/david-wilson.pdf',
      jobId: '3', // UX Designer
      stage: 'completed_success',
      expectedSalary: 75000,
      contactInfo: {
        email: '<EMAIL>',
        phone: '******-0126',
        linkedin: 'https://linkedin.com/in/david-wilson',
        address: '321 Design Street, Portland, OR 97201'
      },
      status: 'completed_success',
      source: 'Indeed',
      createdAt: new Date('2024-01-10T08:30:00Z'),
      updatedAt: new Date('2024-01-18T15:00:00Z'),
      createdBy: '1'
    },
    {
      _id: '5',
      sourceUid: 'cand_005',
      name: 'Eva Martinez',
      email: '<EMAIL>',
      phone: '******-0127',
      resumeLink: 'https://example.com/resumes/eva-martinez.pdf',
      jobId: '2', // Product Manager
      stage: 'registered',
      expectedSalary: 105000,
      contactInfo: {
        email: '<EMAIL>',
        phone: '******-0127',
        linkedin: 'https://linkedin.com/in/eva-martinez',
        address: '654 Innovation Blvd, Denver, CO 80202'
      },
      status: 'registered',
      source: 'Glassdoor',
      createdAt: new Date('2024-01-18T14:20:00Z'),
      updatedAt: new Date('2024-01-18T14:20:00Z'),
      createdBy: '2'
    }
  ],
}

// Mock API disabled - using real backend
// if (import.meta.env.DEV && import.meta.env.VITE_USE_MOCK_API === 'true') {
if (false) {
  console.log('🔧 Using mock API for development')

  // Override API methods with mock responses

  api.get = async function<T>(endpoint: string, params?: Record<string, any>): Promise<T> {
    await new Promise(resolve => setTimeout(resolve, 500)) // Simulate network delay

    // Handle search and filter endpoints
    if (endpoint === '/candidate/search') {
      const filtered = mockApi.candidates.filter((candidate: any) => {
        if (params?.name && !candidate.name.toLowerCase().includes(params.name.toLowerCase())) return false
        if (params?.email && !candidate.email.toLowerCase().includes(params.email.toLowerCase())) return false
        return true
      })
      return filtered as T
    }

    if (endpoint === '/candidate/filter') {
      const filtered = mockApi.candidates.filter((candidate: any) => {
        if (params?.stage && candidate.stage !== params.stage) return false
        if (params?.status && candidate.status !== params.status) return false
        if (params?.jobId && candidate.jobId !== params.jobId) return false
        return true
      })
      return filtered as T
    }

    if (endpoint === '/organization/search') {
      const filtered = mockApi.organizations.filter((org: any) => {
        if (params?.name && !org.name.toLowerCase().includes(params.name.toLowerCase())) return false
        if (params?.domain && !org.domain.toLowerCase().includes(params.domain.toLowerCase())) return false
        if (params?.address && org.address && !org.address.toLowerCase().includes(params.address.toLowerCase())) return false
        return true
      })
      return filtered as T
    }

    // Handle analytics endpoints
    if (endpoint === '/candidate/analytics') {
      return {
        totalCandidates: mockApi.candidates.length,
        candidatesByStage: { registered: 5, interview: 3, hired: 2 },
        candidatesByStatus: { registered: 5, in_progress: 3, completed_success: 2 }
      } as T
    }

    if (endpoint === '/job/analytics') {
      return {
        totalJobs: mockApi.jobs.length,
        jobsByStatus: { active: 3, draft: 2, closed: 1 },
        jobsByDepartment: { Engineering: 4, Marketing: 2 }
      } as T
    }

    if (endpoint === '/organization/analytics') {
      return { totalOrganizations: mockApi.organizations.length } as T
    }

    if (endpoint === '/recruiter/analytics') {
      return {
        totalRecruiters: mockApi.recruiters.length,
        recruitersByOrganization: { 'org1': 2, 'org2': 1 }
      } as T
    }

    // Handle basic endpoints
    if (endpoint === '/organization') {
      return mockApi.organizations as T
    }
    if (endpoint === '/recruiter') {
      return mockApi.recruiters as T
    }
    if (endpoint === '/job') {
      return mockApi.jobs as T
    }
    if (endpoint === '/candidate') {
      return mockApi.candidates as T
    }

    // Handle authentication endpoints
    if (endpoint === '/auth/me') {
      // Always return mock user data for development
      // In a real app, this would check authentication properly
      return {
        _id: 'user123',
        name: 'John Doe',
        email: '<EMAIL>',
        organization: {
          _id: 'org123',
          name: 'Example Corp',
          address: '123 Business St, City, State 12345',
          domain: 'example.com',
          createdAt: '2023-01-01T00:00:00.000Z',
          updatedAt: '2023-01-01T00:00:00.000Z',
          __v: 0
        }
      } as T
    }

    // Handle recruiter-specific endpoints
    if (endpoint === '/recruiter/jobs') {
      return mockApi.jobs.slice(0, 2) as T // Return subset for demo
    }

    if (endpoint === '/recruiter/candidates') {
      return mockApi.candidates.slice(0, 3) as T // Return subset for demo
    }

    // Handle recruiter job candidates
    if (endpoint.startsWith('/recruiter/jobs/') && endpoint.endsWith('/candidates')) {
      const jobId = endpoint.split('/')[3] // Extract jobId from /recruiter/jobs/:jobId/candidates
      const filtered = mockApi.candidates.filter((candidate: any) => candidate.jobId === jobId)
      return filtered as T
    }

    // Handle specific recruiter job
    if (endpoint.startsWith('/recruiter/jobs/') && !endpoint.endsWith('/candidates')) {
      const jobId = endpoint.split('/')[3] // Extract jobId from /recruiter/jobs/:jobId
      const job = mockApi.jobs.find((job: any) => job._id === jobId)
      return job as T
    }

    // Handle job-specific endpoints
    if (endpoint.startsWith('/job/jobs/') && endpoint.endsWith('/candidates')) {
      return mockApi.candidates.slice(0, 2) as T // Return subset for demo
    }

    if (endpoint.startsWith('/job/external/jobs/') && endpoint.endsWith('/candidates')) {
      return [
        {
          id: 'ext_001',
          name: 'Alice Johnson',
          email: '<EMAIL>',
          phone: '+1234567891',
          resumeUrl: 'https://external-ats.com/resumes/alice.pdf',
          source: 'external_ats',
          appliedDate: '2023-09-05T14:30:00.000Z'
        }
      ] as T
    }

    // Handle candidate by job endpoints
    if (endpoint.startsWith('/candidate/job/')) {
      const jobId = endpoint.split('/').pop()
      const filtered = mockApi.candidates.filter((candidate: any) => candidate.jobId === jobId)
      return filtered as T
    }

    // Handle CSV template download
    if (endpoint === '/api/candidate/csv-template') {
      // Create CSV template content
      const csvTemplate = `name,email,phone,resumeLink,jobId,stage,expectedSalary,source,contactInfo.linkedin,contactInfo.github,contactInfo.address
John Doe,<EMAIL>,+1234567890,https://example.com/resume.pdf,job123,registered,75000,website,https://linkedin.com/in/johndoe,https://github.com/johndoe,123 Main St
Jane Smith,<EMAIL>,+0987654321,https://example.com/resume2.pdf,job456,interview,80000,referral,https://linkedin.com/in/janesmith,,456 Oak Ave`

      // Create a blob and return it
      const blob = new Blob([csvTemplate], { type: 'text/csv' })
      return blob as T
    }

    // Handle JobConfig endpoints (updated to /job-config)
    if (endpoint === '/job-config') {
      return {
        success: true,
        data: [],
        message: "No job configurations found"
      } as T
    }

    if (endpoint.startsWith('/job-config/')) {
      const jobId = endpoint.split('/').pop()
      return {
        _id: 'config1',
        jobId: jobId,
        flow: [],
        stageConfig: [],
        createdAt: new Date(),
        updatedAt: new Date()
      } as T
    }

    // Handle authenticated jobs endpoint
    if (endpoint.startsWith('/jobs/')) {
      const jobId = endpoint.split('/').pop()
      const job = mockApi.jobs.find((job: any) => job._id === jobId)
      return job as T
    }

    // Log unhandled endpoints for debugging
    console.warn(`Unhandled GET endpoint: ${endpoint}`, params)

    // Return empty response instead of making real HTTP request
    return [] as T
  }

  api.post = async function<T>(endpoint: string, data?: any): Promise<T> {
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // Handle workflow endpoints (updated path)
    if (endpoint.includes('/workflows/') && endpoint.includes('/start')) {
      return { success: true } as T
    }

    // Handle candidate actions (job-specific)
    if (endpoint.includes('/shortlist') || endpoint.includes('/reject')) {
      return { success: true } as T
    }

    if (endpoint === '/organization') {
      const newOrg = {
        _id: String(mockApi.organizations.length + 1),
        ...data,
        createdAt: new Date(),
        updatedAt: new Date(),
      }
      mockApi.organizations.push(newOrg)
      return newOrg as T
    }

    if (endpoint === '/recruiter') {
      const newRecruiter = {
        _id: String(mockApi.recruiters.length + 1),
        ...data,
        createdAt: new Date(),
        updatedAt: new Date(),
      }
      mockApi.recruiters.push(newRecruiter)
      return newRecruiter as T
    }

    if (endpoint === '/job') {
      const newJob = {
        _id: String(mockApi.jobs.length + 1),
        ...data,
        postedDate: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
      }
      mockApi.jobs.push(newJob)
      return newJob as T
    }

    if (endpoint === '/candidate') {
      const newCandidate = {
        _id: (mockApi.candidates.length + 1).toString(),
        sourceUid: `cand_${String(mockApi.candidates.length + 1).padStart(3, '0')}`,
        ...data,
        createdAt: new Date(),
        updatedAt: new Date(),
      }
      mockApi.candidates.push(newCandidate)
      return newCandidate as T
    }

    // Handle authentication login
    if (endpoint === '/auth/login') {
      // Mock successful login
      localStorage.setItem('isAuthenticated', 'true')
      return {
        token: 'mock-jwt-token'
      } as T
    }

    if (endpoint === '/job-config') {
      const newJobConfig = {
        _id: String(Date.now()),
        ...data,
        createdAt: new Date(),
        updatedAt: new Date(),
      }
      return newJobConfig as T
    }

    // Handle CSV upload
    if (endpoint === '/candidate/upload-csv') {
      const { csvData } = data
      const insertedCandidates: any[] = []
      const errors: any[] = []

      csvData.forEach((candidateData: any, index: number) => {
        try {
          // Basic validation
          if (!candidateData.name || !candidateData.email) {
            errors.push({
              row: index + 2, // +2 because index starts at 0 and we skip header
              data: candidateData,
              errors: ['Name and email are required']
            })
            return
          }

          // Create new candidate
          const newCandidate = {
            _id: (mockApi.candidates.length + insertedCandidates.length + 1).toString(),
            sourceUid: `csv_${String(mockApi.candidates.length + insertedCandidates.length + 1).padStart(3, '0')}`,
            name: candidateData.name,
            contactInfo: {
              email: candidateData.email,
              phone: candidateData.phone || '',
              linkedin: candidateData['contactInfo.linkedin'] || '',
              github: candidateData['contactInfo.github'] || '',
              address: candidateData['contactInfo.address'] || '',
            },
            resumeLink: candidateData.resumeLink || '',
            jobId: candidateData.jobId || '',
            stage: candidateData.stage || 'registered',
            status: 'registered' as any,
            expectedSalary: candidateData.expectedSalary || null,
            source: candidateData.source || 'csv_upload',
            createdAt: new Date(),
            updatedAt: new Date(),
          }

          insertedCandidates.push(newCandidate)
          mockApi.candidates.push(newCandidate)
        } catch (error) {
          errors.push({
            row: index + 2,
            data: candidateData,
            errors: ['Failed to process candidate data']
          })
        }
      })

      const response = {
        message: 'CSV upload completed',
        summary: {
          totalRows: csvData.length,
          successfulInserts: insertedCandidates.length,
          failedRows: errors.length,
        },
        insertedCandidates,
        errors,
      }

      return response as T
    }

    // Log unhandled endpoints for debugging
    console.warn(`Unhandled POST endpoint: ${endpoint}`, data)

    // Return success response instead of making real HTTP request
    return { success: true } as T
  }

  api.put = async function<T>(endpoint: string, data?: any): Promise<T> {
    await new Promise(resolve => setTimeout(resolve, 500))

    const orgMatch = endpoint.match(/\/organization\/(.+)/)
    if (orgMatch) {
      const id = orgMatch[1]
      const orgIndex = mockApi.organizations.findIndex(org => org._id === id)
      if (orgIndex !== -1) {
        mockApi.organizations[orgIndex] = {
          ...mockApi.organizations[orgIndex],
          ...data,
          updatedAt: new Date(),
        }
        return mockApi.organizations[orgIndex] as T
      }
    }

    const recruiterMatch = endpoint.match(/\/recruiter\/(.+)/)
    if (recruiterMatch) {
      const id = recruiterMatch[1]
      const recruiterIndex = mockApi.recruiters.findIndex(recruiter => recruiter._id === id)
      if (recruiterIndex !== -1) {
        mockApi.recruiters[recruiterIndex] = {
          ...mockApi.recruiters[recruiterIndex],
          ...data,
          updatedAt: new Date(),
        }
        return mockApi.recruiters[recruiterIndex] as T
      }
    }

    const jobMatch = endpoint.match(/\/job\/(.+)/)
    if (jobMatch) {
      const id = jobMatch[1]
      const jobIndex = mockApi.jobs.findIndex(job => job._id === id)
      if (jobIndex !== -1) {
        mockApi.jobs[jobIndex] = {
          ...mockApi.jobs[jobIndex],
          ...data,
          updatedAt: new Date(),
        }
        return mockApi.jobs[jobIndex] as T
      }
    }

    const candidateMatch = endpoint.match(/\/candidate\/(.+)/)
    if (candidateMatch) {
      const id = candidateMatch[1]
      const candidateIndex = mockApi.candidates.findIndex(candidate => candidate._id === id)
      if (candidateIndex !== -1) {
        mockApi.candidates[candidateIndex] = {
          ...mockApi.candidates[candidateIndex],
          ...data,
          updatedAt: new Date(),
        }
        return mockApi.candidates[candidateIndex] as T
      }
    }

    const jobConfigMatch = endpoint.match(/\/job-config\/(.+)/)
    if (jobConfigMatch) {
      return { _id: jobConfigMatch[1], ...data, updatedAt: new Date() } as T
    }

    // Log unhandled endpoints for debugging
    console.warn(`Unhandled PUT endpoint: ${endpoint}`, data)

    // Return success response instead of making real HTTP request
    return { success: true } as T
  }

  api.patch = async function<T>(endpoint: string, data?: any): Promise<T> {
    await new Promise(resolve => setTimeout(resolve, 500))

    const orgMatch = endpoint.match(/\/organization\/update\/(.+)/)
    if (orgMatch) {
      const id = orgMatch[1]
      const orgIndex = mockApi.organizations.findIndex(org => org._id === id)
      if (orgIndex !== -1) {
        mockApi.organizations[orgIndex] = {
          ...mockApi.organizations[orgIndex],
          ...data,
          updatedAt: new Date(),
        }
        return mockApi.organizations[orgIndex] as T
      }
    }

    // Handle candidate stage updates
    const candidateStageMatch = endpoint.match(/\/candidate\/(.+)\/stage/)
    if (candidateStageMatch) {
      const id = candidateStageMatch[1]
      const candidateIndex = mockApi.candidates.findIndex(candidate => candidate._id === id)
      if (candidateIndex !== -1) {
        mockApi.candidates[candidateIndex] = {
          ...mockApi.candidates[candidateIndex],
          ...data,
          updatedAt: new Date(),
        }
        return mockApi.candidates[candidateIndex] as T
      }
    }

    // Log unhandled endpoints for debugging
    console.warn(`Unhandled PATCH endpoint: ${endpoint}`, data)

    // Return success response instead of making real HTTP request
    return { success: true } as T
  }

  api.delete = async function<T>(endpoint: string): Promise<T> {
    await new Promise(resolve => setTimeout(resolve, 500))

    // Handle organization delete with special prefix
    const orgDeleteMatch = endpoint.match(/\/organization\/delete\/(.+)/)
    if (orgDeleteMatch) {
      const id = orgDeleteMatch[1]
      const orgIndex = mockApi.organizations.findIndex(org => org._id === id)
      if (orgIndex !== -1) {
        const deleted = mockApi.organizations.splice(orgIndex, 1)[0]
        return deleted as T
      }
    }

    const recruiterMatch = endpoint.match(/\/recruiter\/(.+)/)
    if (recruiterMatch) {
      const id = recruiterMatch[1]
      const recruiterIndex = mockApi.recruiters.findIndex(recruiter => recruiter._id === id)
      if (recruiterIndex !== -1) {
        const deleted = mockApi.recruiters.splice(recruiterIndex, 1)[0]
        return deleted as T
      }
    }

    const jobMatch = endpoint.match(/\/job\/(.+)/)
    if (jobMatch) {
      const id = jobMatch[1]
      const jobIndex = mockApi.jobs.findIndex(job => job._id === id)
      if (jobIndex !== -1) {
        const deleted = mockApi.jobs.splice(jobIndex, 1)[0]
        return deleted as T
      }
    }

    const candidateMatch = endpoint.match(/\/candidate\/(.+)/)
    if (candidateMatch) {
      const id = candidateMatch[1]
      const candidateIndex = mockApi.candidates.findIndex(candidate => candidate._id === id)
      if (candidateIndex !== -1) {
        const deleted = mockApi.candidates.splice(candidateIndex, 1)[0]
        return deleted as T
      }
    }

    const jobConfigMatch = endpoint.match(/\/job-config\/(.+)/)
    if (jobConfigMatch) {
      const jobId = jobConfigMatch[1]
      // Simulate finding and deleting the job configuration
      const deletedJobConfig = {
        _id: `config-${jobId}`,
        jobId: jobId,
        flow: [],
        stageConfig: [],
        createdAt: new Date(),
        updatedAt: new Date()
      }
      return { deletedJobConfig } as T
    }

    // Log unhandled endpoints for debugging
    console.warn(`Unhandled DELETE endpoint: ${endpoint}`)

    // Return success response instead of making real HTTP request
    return { success: true } as T
  }
}