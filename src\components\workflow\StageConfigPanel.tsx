import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { X, Plus, Trash2, Settings, Clock, MessageSquare, Zap, FileText } from 'lucide-react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

import { useWorkflowStore, stageAgentMappings } from '@/stores/workflowStore'
import { FlowConfigPanel } from './FlowConfigPanel'
import { z } from 'zod'
import type { ICommunicationChannel, IStageScheduling, IScreeningQuestion } from '@/types'

// Assessment question schema
const assessmentQuestionSchema = z.object({
  topic: z.string().min(1, 'Topic is required'),
  questionText: z.string().min(1, 'Question text is required'),
  followUpQuestions: z.array(z.string()).optional()
})

// Screening question schema
const screeningQuestionSchema = z.object({
  id: z.number(),
  question: z.string().min(1, 'Question is required'),
  type: z.enum(['multiple-choice', 'yes-no', 'text', 'rating']).optional(),
  options: z.array(z.string()).optional(),
  correctAnswer: z.string().optional(),
  required: z.boolean().optional()
})

// Validation schema for stage configuration
const stageConfigSchema = z.object({
  agentId: z.string().min(1, 'Agent ID is required'),
  outputs: z.array(z.string().min(1, 'Output cannot be empty')).min(1, 'At least one output is required'),
  params: z.record(z.string(), z.any()).optional(),
  communicationChannel: z.enum(['EMAIL', 'PLIVO', 'WHATSAPP', 'SLACK', 'CALENDAR']).optional(),
  scheduling: z.object({
    type: z.enum(['IMMEDIATE', 'BUSINESS_HOURS']),
    params: z.object({
      timezone: z.string().optional(),
      startHour: z.number().min(0).max(23).optional(),
      endHour: z.number().min(0).max(23).optional(),
    }).optional(),
  }).optional(),
  // Assessment-specific fields
  assessmentType: z.enum(['ai-interview', 'manual']).optional(),
  questions: z.array(assessmentQuestionSchema).optional(),
  assessmentPlatform: z.string().optional(),
  assessmentLink: z.string().optional(),
  isProctoringEnabled: z.boolean().optional()
})

type StageConfigFormData = z.infer<typeof stageConfigSchema>



// Communication channels
const communicationChannels: { value: ICommunicationChannel; label: string; icon: any }[] = [
  { value: 'EMAIL', label: 'Email', icon: MessageSquare },
  { value: 'PLIVO', label: 'Phone (Plivo)', icon: MessageSquare },
  { value: 'WHATSAPP', label: 'WhatsApp', icon: MessageSquare },
  { value: 'SLACK', label: 'Slack', icon: MessageSquare },
  { value: 'CALENDAR', label: 'Calendar', icon: Clock },
]

export function StageConfigPanel() {
  const { selectedNode, isConfigPanelOpen, closeConfigPanel, updateNodeData, removeNode, updateStageFlow } = useWorkflowStore()
  const [customParams, setCustomParams] = useState<Array<{ key: string; value: string }>>([])
  // Assessment type state - will be managed through form
  const [assessmentType, setAssessmentType] = useState<'ai-interview' | 'manual'>('ai-interview')

  // Flow configuration state
  const [flowMappings, setFlowMappings] = useState<{ outcome: string; targetStage: string }[]>([])
  const [hasUserResetMappings, setHasUserResetMappings] = useState(false)

  const [questions, setQuestions] = useState<Array<{ topic: string; questionText: string; followUpQuestions: string[] }>>([])
  const [screeningQuestions, setScreeningQuestions] = useState<IScreeningQuestion[]>([])

  // Get static mapping for the selected stage
  const stageMapping = selectedNode ? stageAgentMappings[selectedNode.data.stage as keyof typeof stageAgentMappings] : null
  const isAssessmentStage = selectedNode?.data.stage === 'assessment'
  const isScreeningStage = selectedNode?.data.stage === 'screening'

  const {
    register,
    handleSubmit,
    control,
    setValue,
    watch,
    reset,
    formState: { errors, isSubmitting }
  } = useForm<StageConfigFormData>({
    resolver: zodResolver(stageConfigSchema),
    mode: 'onChange',
    defaultValues: {
      agentId: '',
      outputs: ['success', 'fail'],
      params: {},
      communicationChannel: undefined,
      scheduling: {
        type: 'IMMEDIATE',
        params: {}
      }
    }
  })



  const watchSchedulingType = watch('scheduling.type')
  const watchCommunicationChannel = watch('communicationChannel')



  // Load existing configuration when node is selected
  useEffect(() => {
    if (selectedNode && isConfigPanelOpen) {
      const data = selectedNode.data


      // Reset form first to clear any previous values
      reset({
        agentId: data.agentId || '',
        outputs: data.outputs || ['success', 'fail'],
        communicationChannel: data.communicationChannel,
        scheduling: data.scheduling || { type: 'IMMEDIATE', params: {} }
      })

      // Set assessment type first before other form updates
      if (isAssessmentStage && data.params?.assessmentType) {
        setAssessmentType(data.params.assessmentType as 'ai-interview' | 'manual')
      }

      // Force update the form values after reset
      setTimeout(() => {
        setValue('communicationChannel', data.communicationChannel, { shouldValidate: true })
        setValue('scheduling.type', data.scheduling?.type || 'IMMEDIATE', { shouldValidate: true })
      }, 10)

      // Load custom parameters
      if (data.params) {
        // Filter out assessment-specific params for custom params display
        const filteredParams = { ...data.params }
        delete filteredParams.questions
        delete filteredParams.assessmentType
        delete filteredParams.assessmentDetails
        delete filteredParams.isProctoringEnabled

        const paramEntries = Object.entries(filteredParams).map(([key, value]) => ({
          key,
          value: String(value)
        }))
        setCustomParams(paramEntries)

        // Load assessment-specific configuration details
        if (isAssessmentStage && data.params?.assessmentType) {


          if (data.params.assessmentType === 'ai-interview' && data.params.questions) {
            setQuestions(data.params.questions)
          } else if (data.params.assessmentType === 'manual' && data.params.assessmentDetails) {
            // Set the assessment platform and link values
            const assessmentDetails = data.params.assessmentDetails

            setTimeout(() => {
              setValue('assessmentPlatform', assessmentDetails.assessmentPlatform || '')
              setValue('assessmentLink', assessmentDetails.assessmentLink || '')
            }, 20)
          }
        }

        // Load screening-specific configuration details
        if (isScreeningStage) {
          const existingConfig = data.params?.screeningConfig
          if (existingConfig?.questions) {
            setScreeningQuestions(existingConfig.questions.map(q => ({
              ...q,
              options: q.options ? [...q.options] : undefined
            })))
          } else if (stageMapping && 'screeningConfig' in stageMapping.defaultParams && stageMapping.defaultParams.screeningConfig?.questions) {
            // Use default questions from stage mapping
            setScreeningQuestions(stageMapping.defaultParams.screeningConfig.questions.map(q => ({
              ...q,
              options: q.options ? [...q.options] : undefined
            })))
          }
        }

        // Load existing flow mappings from current job config (only if user hasn't reset)
        if (!hasUserResetMappings) {
          const { currentJobConfig } = useWorkflowStore.getState()
          if (currentJobConfig?.flow) {
            const stageFlow = currentJobConfig.flow.find(f => f.stage === selectedNode.id)
            if (stageFlow?.next) {
              const mappings = stageFlow.next.map(n => ({
                outcome: n.outcome,
                targetStage: n.stage
              }))
              console.log('Loading existing flow mappings for stage:', selectedNode.id, mappings.map(m => `${m.outcome} → ${m.targetStage}`))
              setFlowMappings(mappings)
            } else {
              // No existing flow mappings, reset to empty so FlowConfigPanel can initialize defaults
              console.log('No existing flow mappings for stage:', selectedNode.id, 'resetting to empty')
              setFlowMappings([])
            }
          } else {
            // No job config flow, reset to empty
            setFlowMappings([])
          }
        }
      }
    }
  }, [selectedNode?.id, isConfigPanelOpen, isAssessmentStage, reset])

  // Reset the user reset flag when switching stages
  useEffect(() => {
    setHasUserResetMappings(false)
  }, [selectedNode?.id])

  const handleSave = async (data: StageConfigFormData) => {
    if (!selectedNode) return

    // Convert custom params array to object
    const paramsObject = customParams.reduce((acc, param) => {
      if (param.key && param.value) {
        acc[param.key] = param.value
      }
      return acc
    }, {} as Record<string, any>)

    // Add assessment-specific configuration for assessment stage
    if (isAssessmentStage) {
      if (assessmentType === 'ai-interview') {
        paramsObject.questions = questions.filter(q => q.topic && q.questionText)
        paramsObject.assessmentType = 'ai-interview'
        paramsObject.isProctoringEnabled = data.isProctoringEnabled || false
      } else if (assessmentType === 'manual') {
        paramsObject.assessmentDetails = {
          assessmentPlatform: data.assessmentPlatform || '',
          assessmentLink: data.assessmentLink || ''
        }
        paramsObject.assessmentType = 'manual'
      }
    }

    // Add screening-specific configuration for screening stage
    if (isScreeningStage) {
      paramsObject.screeningConfig = {
        questions: screeningQuestions.filter(q => q.question.trim() !== ''),
        passingScore: 80, // Default passing score
        timeLimit: 15, // Default time limit in minutes
        allowRetries: false,
        maxRetries: 0
      }
    }

    // Update node data
    // Clean up scheduling data based on type
    const cleanedScheduling: IStageScheduling = data.scheduling ? {
      type: data.scheduling.type,
      params: data.scheduling.type === 'BUSINESS_HOURS' ? (data.scheduling.params || {}) : {}
    } : { type: 'IMMEDIATE', params: {} }

    updateNodeData(selectedNode.id, {
      agentId: data.agentId,
      outputs: data.outputs,
      params: paramsObject,
      communicationChannel: data.communicationChannel,
      scheduling: cleanedScheduling,
      isConfigured: true
    })

    // Save flow mappings
    console.log('Saving flow mappings for stage:', selectedNode.id, flowMappings.map(m => `${m.outcome} → ${m.targetStage}`))
    console.log('Full flowMappings object:', flowMappings)
    if (flowMappings.length > 0) {
      updateStageFlow(selectedNode.id, flowMappings)
    }

    closeConfigPanel()
  }

  const addCustomParam = () => {
    setCustomParams([...customParams, { key: '', value: '' }])
  }

  const removeCustomParam = (index: number) => {
    setCustomParams(customParams.filter((_, i) => i !== index))
  }

  const updateCustomParam = (index: number, field: 'key' | 'value', value: string) => {
    const updated = [...customParams]
    updated[index][field] = value
    setCustomParams(updated)
  }

  if (!isConfigPanelOpen || !selectedNode) {
    return null
  }

  return (
    <div className="fixed inset-y-0 right-0 w-96 bg-white shadow-xl border-l border-gray-200 z-50 overflow-y-auto">
      <div className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-lg font-semibold text-gray-900">Configure Stage</h2>
            <p className="text-sm text-gray-600">{selectedNode.data.label}</p>
          </div>
          <Button variant="ghost" size="sm" onClick={closeConfigPanel}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        <form onSubmit={handleSubmit(handleSave)} className="space-y-6">
          {/* Agent Information (Read-Only) */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center space-x-2">
                <Zap className="h-4 w-4" />
                <span>Agent Configuration</span>
                <Badge variant="secondary" className="ml-auto text-xs">Static</Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-gray-50 p-3 rounded-lg">
                <Label className="text-sm font-medium text-gray-700">Assigned Agent</Label>
                <div className="mt-1">
                  <div className="font-medium text-gray-900">{stageMapping?.agentId || 'No agent assigned'}</div>
                  <div className="text-xs text-gray-500 mt-1">
                    {stageMapping?.agentId === 'reviewAgent' && 'Automated resume review and scoring'}
                    {stageMapping?.agentId === 'screeningAgent' && 'Phone/video screening automation'}
                    {stageMapping?.agentId === 'assessmentAgent' && 'Technical assessment management'}
                    {stageMapping?.agentId === 'interviewAgent' && 'Interview scheduling and coordination'}
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 p-3 rounded-lg">
                <Label className="text-sm font-medium text-gray-700">Stage Outputs</Label>
                <div className="mt-2 flex flex-wrap gap-2">
                  {stageMapping?.outputs.map((output, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {output}
                    </Badge>
                  ))}
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  These outputs are predefined for this stage type
                </div>
              </div>
            </CardContent>
          </Card>



          {/* Communication Channel */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center space-x-2">
                <MessageSquare className="h-4 w-4" />
                <span>Communication Channel</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Select onValueChange={(value) => setValue('communicationChannel', value as ICommunicationChannel)} value={watchCommunicationChannel || ''}>
                <SelectTrigger>
                  <SelectValue placeholder="Select communication method" />
                </SelectTrigger>
                <SelectContent>
                  {communicationChannels.map((channel) => (
                    <SelectItem key={channel.value} value={channel.value}>
                      <div className="flex items-center space-x-2">
                        <channel.icon className="h-4 w-4" />
                        <span>{channel.label}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </CardContent>
          </Card>

          {/* Scheduling Configuration */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center space-x-2">
                <Clock className="h-4 w-4" />
                <span>Scheduling</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label>Execution Type</Label>
                <Select onValueChange={(value) => setValue('scheduling.type', value as 'IMMEDIATE' | 'BUSINESS_HOURS')} value={watchSchedulingType || 'IMMEDIATE'}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="IMMEDIATE">Immediate</SelectItem>
                    <SelectItem value="BUSINESS_HOURS">Business Hours Only</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {watchSchedulingType === 'BUSINESS_HOURS' && (
                <div className="space-y-3">
                  <div>
                    <Label htmlFor="timezone">Timezone</Label>
                    <Input
                      id="timezone"
                      {...register('scheduling.params.timezone')}
                      placeholder="e.g., America/New_York"
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <Label htmlFor="startHour">Start Hour</Label>
                      <Input
                        id="startHour"
                        type="number"
                        min="0"
                        max="23"
                        {...register('scheduling.params.startHour', { valueAsNumber: true })}
                        placeholder="9"
                      />
                    </div>
                    <div>
                      <Label htmlFor="endHour">End Hour</Label>
                      <Input
                        id="endHour"
                        type="number"
                        min="0"
                        max="23"
                        {...register('scheduling.params.endHour', { valueAsNumber: true })}
                        placeholder="17"
                      />
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Custom Parameters */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center space-x-2">
                <Settings className="h-4 w-4" />
                <span>Custom Parameters</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {customParams.map((param, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <Input
                    placeholder="Parameter name"
                    value={param.key}
                    onChange={(e) => updateCustomParam(index, 'key', e.target.value)}
                    className="flex-1"
                  />
                  <Input
                    placeholder="Value"
                    value={param.value}
                    onChange={(e) => updateCustomParam(index, 'value', e.target.value)}
                    className="flex-1"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeCustomParam(index)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              ))}
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addCustomParam}
                className="w-full"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Parameter
              </Button>
            </CardContent>
          </Card>

          {/* Assessment Configuration - Only for Assessment Stage */}
          {isAssessmentStage && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center space-x-2">
                  <FileText className="h-4 w-4" />
                  <span>Assessment Configuration</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Assessment Type Selection */}
                <div>
                  <Label className="text-sm font-medium">Assessment Type</Label>
                  <Select value={assessmentType} onValueChange={(value: 'ai-interview' | 'manual') => setAssessmentType(value)}>
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Select assessment type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ai-interview">AI Interview</SelectItem>
                      <SelectItem value="manual">Manual</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* AI Interview Assessment Configuration */}
                {assessmentType === 'ai-interview' && (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <Label className="text-sm font-medium">Assessment Questions</Label>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => setQuestions([...questions, { topic: '', questionText: '', followUpQuestions: [] }])}
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Add Question
                      </Button>
                    </div>

                    {questions.map((question, qIndex) => (
                      <Card key={qIndex} className="p-4 border-l-4 border-l-blue-500">
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <Label className="text-sm font-medium">Question {qIndex + 1}</Label>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => setQuestions(questions.filter((_, i) => i !== qIndex))}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>

                          <div>
                            <Label className="text-xs text-gray-600">Topic</Label>
                            <Input
                              placeholder="e.g., nodejs, reactjs, express"
                              value={question.topic}
                              onChange={(e) => {
                                const updated = [...questions]
                                updated[qIndex].topic = e.target.value
                                setQuestions(updated)
                              }}
                              className="mt-1"
                            />
                          </div>

                          <div>
                            <Label className="text-xs text-gray-600">Question Text</Label>
                            <Textarea
                              placeholder="Enter the main question..."
                              value={question.questionText}
                              onChange={(e) => {
                                const updated = [...questions]
                                updated[qIndex].questionText = e.target.value
                                setQuestions(updated)
                              }}
                              className="mt-1"
                              rows={2}
                            />
                          </div>

                          <div>
                            <Label className="text-xs text-gray-600">Follow-up Questions</Label>
                            {question.followUpQuestions.map((followUp, fIndex) => (
                              <div key={fIndex} className="flex items-center space-x-2 mt-2">
                                <Input
                                  placeholder="Follow-up question..."
                                  value={followUp}
                                  onChange={(e) => {
                                    const updated = [...questions]
                                    updated[qIndex].followUpQuestions[fIndex] = e.target.value
                                    setQuestions(updated)
                                  }}
                                  className="flex-1"
                                />
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => {
                                    const updated = [...questions]
                                    updated[qIndex].followUpQuestions = updated[qIndex].followUpQuestions.filter((_, i) => i !== fIndex)
                                    setQuestions(updated)
                                  }}
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            ))}
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                const updated = [...questions]
                                updated[qIndex].followUpQuestions.push('')
                                setQuestions(updated)
                              }}
                              className="mt-2 w-full"
                            >
                              <Plus className="h-4 w-4 mr-2" />
                              Add Follow-up
                            </Button>
                          </div>
                        </div>
                      </Card>
                    ))}

                    {/* Proctoring for AI Interview */}
                    <div className="space-y-4 pt-4 border-t">
                      <div className="flex items-center space-x-2">
                        <input
                          {...register('isProctoringEnabled')}
                          type="checkbox"
                          id="proctoring"
                          className="rounded"
                        />
                        <Label htmlFor="proctoring" className="text-sm">Enable Proctoring</Label>
                      </div>
                    </div>
                  </div>
                )}

                {/* Manual Assessment Configuration */}
                {assessmentType === 'manual' && (
                  <div className="space-y-4">
                    <div>
                      <Label className="text-sm font-medium">Assessment Platform</Label>
                      <Input
                        {...register('assessmentPlatform')}
                        placeholder="e.g., HackerRank, Codility, LeetCode"
                        className="mt-1"
                      />
                    </div>

                    <div>
                      <Label className="text-sm font-medium">Assessment Link</Label>
                      <Input
                        {...register('assessmentLink')}
                        placeholder="https://www.hackerrank.com/..."
                        className="mt-1"
                      />
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Screening Stage Configuration */}
          {isScreeningStage && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Screening Questions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label className="text-sm font-medium">Questions</Label>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => setScreeningQuestions([...screeningQuestions, {
                      id: Date.now(),
                      question: '',
                      type: 'yes-no',
                      options: ['Yes', 'No'],
                      required: true
                    }])}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Question
                  </Button>
                </div>

                {screeningQuestions.map((question, qIndex) => (
                  <Card key={question.id} className="p-4 border-l-4 border-l-yellow-500">
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <Label className="text-sm font-medium">Question {qIndex + 1}</Label>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => setScreeningQuestions(screeningQuestions.filter((_, i) => i !== qIndex))}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>

                      <div>
                        <Label className="text-xs text-gray-600">Question Text</Label>
                        <Textarea
                          placeholder="Enter the screening question..."
                          value={question.question}
                          onChange={(e) => {
                            const updated = [...screeningQuestions]
                            updated[qIndex].question = e.target.value
                            setScreeningQuestions(updated)
                          }}
                          className="mt-1"
                          rows={2}
                        />
                      </div>

                      <div className="grid grid-cols-2 gap-3">
                        <div>
                          <Label className="text-xs text-gray-600">Question Type</Label>
                          <Select
                            value={question.type || 'yes-no'}
                            onValueChange={(value: 'multiple-choice' | 'yes-no' | 'text' | 'rating') => {
                              const updated = [...screeningQuestions]
                              updated[qIndex].type = value
                              // Set default options based on type
                              if (value === 'yes-no') {
                                updated[qIndex].options = ['Yes', 'No']
                              } else if (value === 'rating') {
                                updated[qIndex].options = ['1', '2', '3', '4', '5']
                              } else if (value === 'text') {
                                updated[qIndex].options = undefined
                              }
                              setScreeningQuestions(updated)
                            }}
                          >
                            <SelectTrigger className="mt-1">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="yes-no">Yes/No</SelectItem>
                              <SelectItem value="multiple-choice">Multiple Choice</SelectItem>
                              <SelectItem value="text">Text</SelectItem>
                              <SelectItem value="rating">Rating</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="flex items-center space-x-2 pt-6">
                          <input
                            type="checkbox"
                            id={`required-${question.id}`}
                            checked={question.required || false}
                            onChange={(e) => {
                              const updated = [...screeningQuestions]
                              updated[qIndex].required = e.target.checked
                              setScreeningQuestions(updated)
                            }}
                            className="rounded"
                          />
                          <Label htmlFor={`required-${question.id}`} className="text-xs">Required</Label>
                        </div>
                      </div>

                      {/* Options for multiple choice and rating */}
                      {(question.type === 'multiple-choice' || question.type === 'yes-no' || question.type === 'rating') && (
                        <div>
                          <Label className="text-xs text-gray-600">Options</Label>
                          {question.options?.map((option, oIndex) => (
                            <div key={oIndex} className="flex items-center space-x-2 mt-2">
                              <Input
                                placeholder="Option text..."
                                value={option}
                                onChange={(e) => {
                                  const updated = [...screeningQuestions]
                                  if (updated[qIndex].options) {
                                    updated[qIndex].options![oIndex] = e.target.value
                                    setScreeningQuestions(updated)
                                  }
                                }}
                                className="flex-1"
                              />
                              {question.type === 'multiple-choice' && question.options && question.options.length > 2 && (
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => {
                                    const updated = [...screeningQuestions]
                                    if (updated[qIndex].options) {
                                      updated[qIndex].options = updated[qIndex].options!.filter((_, i) => i !== oIndex)
                                      setScreeningQuestions(updated)
                                    }
                                  }}
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              )}
                            </div>
                          ))}
                          {question.type === 'multiple-choice' && (
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                const updated = [...screeningQuestions]
                                if (!updated[qIndex].options) {
                                  updated[qIndex].options = []
                                }
                                updated[qIndex].options!.push('')
                                setScreeningQuestions(updated)
                              }}
                              className="mt-2 w-full"
                            >
                              <Plus className="h-4 w-4 mr-2" />
                              Add Option
                            </Button>
                          )}
                        </div>
                      )}

                      {/* Correct Answer for scoring */}
                      {(question.type === 'multiple-choice' || question.type === 'yes-no') && (
                        <div>
                          <Label className="text-xs text-gray-600">Correct Answer (for scoring)</Label>
                          <Select
                            value={question.correctAnswer || ''}
                            onValueChange={(value) => {
                              const updated = [...screeningQuestions]
                              updated[qIndex].correctAnswer = value
                              setScreeningQuestions(updated)
                            }}
                          >
                            <SelectTrigger className="mt-1">
                              <SelectValue placeholder="Select correct answer..." />
                            </SelectTrigger>
                            <SelectContent>
                              {question.options?.map((option, oIndex) => (
                                <SelectItem key={oIndex} value={option}>{option}</SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      )}
                    </div>
                  </Card>
                ))}
              </CardContent>
            </Card>
          )}

          {/* Flow Configuration */}
          <FlowConfigPanel
            stageId={selectedNode?.id || ''}
            outputs={selectedNode?.data?.outputs || []}
            currentMappings={flowMappings}
            onMappingsChange={setFlowMappings}
            onReset={() => setHasUserResetMappings(true)}
          />

          {/* Actions */}
          <div className="space-y-3 pt-4">
            <div className="flex space-x-3">
              <Button type="submit" disabled={isSubmitting} className="flex-1">
                {isSubmitting ? 'Saving...' : 'Save Configuration'}
              </Button>
              <Button type="button" variant="outline" onClick={closeConfigPanel}>
                Cancel
              </Button>
            </div>

            {/* Remove Stage Button */}
            <div className="pt-2 border-t border-gray-200">
              <Button
                type="button"
                variant="destructive"
                size="sm"
                onClick={() => {
                  if (selectedNode) {
                    removeNode(selectedNode.id)
                    closeConfigPanel()
                  }
                }}
                className="w-full"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Remove This Stage
              </Button>
            </div>
          </div>
        </form>
      </div>
    </div>
  )
}